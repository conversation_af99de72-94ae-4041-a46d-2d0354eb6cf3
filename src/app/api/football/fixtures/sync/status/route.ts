/**
 * Football Fixtures Sync Status Route
 * Handles sync status monitoring
 */

import { NextRequest } from 'next/server';
import { handleProxyRequest } from '@/lib/api-utils';
import { API_ENDPOINTS, HTTP_METHODS } from '@/lib/api-config';

/**
 * GET /api/football/fixtures/sync/status
 * Get current sync operation status and system health
 * Requires: Authorization header with <PERSON><PERSON> token
 * 
 * Response:
 * {
 *   "success": true,
 *   "data": {
 *     "currentSync": {
 *       "id": "sync-id",
 *       "status": "running",
 *       "progress": 65,
 *       "startedAt": "2024-05-24T10:00:00Z",
 *       "estimatedCompletion": "2024-05-24T10:05:00Z",
 *       "currentOperation": "Processing Premier League fixtures",
 *       "statistics": {
 *         "totalToProcess": 1000,
 *         "processed": 650,
 *         "created": 25,
 *         "updated": 100,
 *         "skipped": 525,
 *         "errors": 0
 *       }
 *     },
 *     "lastSync": {
 *       "id": "last-sync-id",
 *       "status": "success",
 *       "completedAt": "2024-05-24T09:00:00Z",
 *       "duration": 280000,
 *       "statistics": {
 *         "totalProcessed": 950,
 *         "created": 30,
 *         "updated": 180,
 *         "skipped": 740,
 *         "errors": 0
 *       }
 *     },
 *     "systemHealth": {
 *       "apiConnectivity": "healthy",
 *       "databaseStatus": "healthy",
 *       "queueStatus": "healthy",
 *       "lastHealthCheck": "2024-05-24T10:30:00Z"
 *     },
 *     "nextScheduledSync": "2024-05-25T06:00:00Z",
 *     "syncSettings": {
 *       "autoSyncEnabled": true,
 *       "syncInterval": "daily",
 *       "maxRetries": 3,
 *       "timeoutMinutes": 30
 *     }
 *   }
 * }
 */
export async function GET(request: NextRequest) {
  return handleProxyRequest(
    request,
    API_ENDPOINTS.FOOTBALL.FIXTURES_SYNC_STATUS,
    [HTTP_METHODS.GET]
  );
}
